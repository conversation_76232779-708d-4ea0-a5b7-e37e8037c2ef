-- Migration for new features: Performance Alerts and Inventory Management

-- Alert Settings Table
CREATE TABLE IF NOT EXISTS public.AlertSettings (
    id BIGSERIAL PRIMARY KEY,
    shopDomain VARCHAR(255) UNIQUE NOT NULL,
    lowPerformanceThreshold INTEGER DEFAULT 30,
    highPerformanceThreshold INTEGER DEFAULT 80,
    outOfStockAlerts BOOLEAN DEFAULT TRUE,
    lowInventoryThreshold INTEGER DEFAULT 10,
    frequency VARCHAR(50) DEFAULT 'daily',
    emailNotifications BOOLEAN DEFAULT TRUE,
    createdAt TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Dismissed Alerts Table
CREATE TABLE IF NOT EXISTS public.DismissedAlerts (
    id BIGSERIAL PRIMARY KEY,
    shopDomain VARCHAR(255) NOT NULL,
    alertId VARCHAR(255) NOT NULL,
    dismissedAt TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(shopDomain, alertId)
);

-- Inventory Settings Table
CREATE TABLE IF NOT EXISTS public.InventorySettings (
    id BIGSERIAL PRIMARY KEY,
    shopDomain VARCHAR(255) UNIQUE NOT NULL,
    autoHideOutOfStock BOOLEAN DEFAULT TRUE,
    promoteHighInventory BOOLEAN DEFAULT TRUE,
    lowInventoryThreshold INTEGER DEFAULT 10,
    highInventoryThreshold INTEGER DEFAULT 100,
    updateFrequency VARCHAR(50) DEFAULT 'daily',
    createdAt TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Product Performance Tracking Table
CREATE TABLE IF NOT EXISTS public.ProductPerformance (
    id BIGSERIAL PRIMARY KEY,
    shopDomain VARCHAR(255) NOT NULL,
    productId VARCHAR(255) NOT NULL,
    collectionId VARCHAR(255) NOT NULL,
    viewCount INTEGER DEFAULT 0,
    clickCount INTEGER DEFAULT 0,
    conversionCount INTEGER DEFAULT 0,
    performanceScore INTEGER DEFAULT 0,
    position INTEGER DEFAULT 0,
    lastUpdated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(shopDomain, productId, collectionId)
);

-- Collection Analytics Table
CREATE TABLE IF NOT EXISTS public.CollectionAnalytics (
    id BIGSERIAL PRIMARY KEY,
    shopDomain VARCHAR(255) NOT NULL,
    collectionId VARCHAR(255) NOT NULL,
    date DATE NOT NULL,
    views INTEGER DEFAULT 0,
    uniqueViews INTEGER DEFAULT 0,
    clicks INTEGER DEFAULT 0,
    conversions INTEGER DEFAULT 0,
    revenue DECIMAL(10,2) DEFAULT 0.00,
    bounceRate DECIMAL(5,2) DEFAULT 0.00,
    averageTimeOnPage INTEGER DEFAULT 0,
    createdAt TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(shopDomain, collectionId, date)
);

-- Alert History Table
CREATE TABLE IF NOT EXISTS public.AlertHistory (
    id BIGSERIAL PRIMARY KEY,
    shopDomain VARCHAR(255) NOT NULL,
    alertType VARCHAR(100) NOT NULL,
    alertTitle VARCHAR(255) NOT NULL,
    alertDescription TEXT,
    collectionId VARCHAR(255),
    productId VARCHAR(255),
    severity VARCHAR(50) DEFAULT 'info',
    isRead BOOLEAN DEFAULT FALSE,
    isDismissed BOOLEAN DEFAULT FALSE,
    actionTaken VARCHAR(255),
    createdAt TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    dismissedAt TIMESTAMP WITH TIME ZONE
);

-- Product Visibility Tracking Table
CREATE TABLE IF NOT EXISTS public.ProductVisibility (
    id BIGSERIAL PRIMARY KEY,
    shopDomain VARCHAR(255) NOT NULL,
    productId VARCHAR(255) NOT NULL,
    collectionId VARCHAR(255) NOT NULL,
    isVisible BOOLEAN DEFAULT TRUE,
    hiddenReason VARCHAR(255),
    lastUpdated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updatedBy VARCHAR(100) DEFAULT 'system',
    UNIQUE(shopDomain, productId, collectionId)
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_alert_settings_shop ON public.AlertSettings(shopDomain);
CREATE INDEX IF NOT EXISTS idx_dismissed_alerts_shop ON public.DismissedAlerts(shopDomain);
CREATE INDEX IF NOT EXISTS idx_inventory_settings_shop ON public.InventorySettings(shopDomain);
CREATE INDEX IF NOT EXISTS idx_product_performance_shop ON public.ProductPerformance(shopDomain);
CREATE INDEX IF NOT EXISTS idx_product_performance_collection ON public.ProductPerformance(collectionId);
CREATE INDEX IF NOT EXISTS idx_collection_analytics_shop ON public.CollectionAnalytics(shopDomain);
CREATE INDEX IF NOT EXISTS idx_collection_analytics_date ON public.CollectionAnalytics(date);
CREATE INDEX IF NOT EXISTS idx_alert_history_shop ON public.AlertHistory(shopDomain);
CREATE INDEX IF NOT EXISTS idx_alert_history_created ON public.AlertHistory(createdAt);
CREATE INDEX IF NOT EXISTS idx_product_visibility_shop ON public.ProductVisibility(shopDomain);
CREATE INDEX IF NOT EXISTS idx_product_visibility_product ON public.ProductVisibility(productId);

-- Add triggers for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updatedAt = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to relevant tables
DROP TRIGGER IF EXISTS update_alert_settings_updated_at ON public.AlertSettings;
CREATE TRIGGER update_alert_settings_updated_at 
    BEFORE UPDATE ON public.AlertSettings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_inventory_settings_updated_at ON public.InventorySettings;
CREATE TRIGGER update_inventory_settings_updated_at 
    BEFORE UPDATE ON public.InventorySettings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_product_performance_updated_at ON public.ProductPerformance;
CREATE TRIGGER update_product_performance_updated_at 
    BEFORE UPDATE ON public.ProductPerformance 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_product_visibility_updated_at ON public.ProductVisibility;
CREATE TRIGGER update_product_visibility_updated_at 
    BEFORE UPDATE ON public.ProductVisibility 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
