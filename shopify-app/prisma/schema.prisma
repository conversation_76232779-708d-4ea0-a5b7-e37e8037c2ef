// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

// Note that some adapters may set a maximum length for the String type by default, please ensure your strings are long
// enough when changing adapters.
// See https://www.prisma.io/docs/orm/reference/prisma-schema-reference#string for more information
datasource db {
  provider = "sqlite"
  url      = "file:dev.sqlite"
}

model Session {
  id            String    @id
  shop          String
  state         String
  isOnline      <PERSON>an   @default(false)
  scope         String?
  expires       DateTime?
  accessToken   String
  userId        BigInt?
  firstName     String?
  lastName      String?
  email         String?
  accountOwner  Boolean   @default(false)
  locale        String?
  collaborator  <PERSON><PERSON><PERSON>?  @default(false)
  emailVerified <PERSON>olean?  @default(false)
}

model AlertSettings {
  id                      BigInt   @id @default(autoincrement())
  shopDomain              String   @unique
  lowPerformanceThreshold Int      @default(30)
  highPerformanceThreshold Int     @default(80)
  outOfStockAlerts        Boolean  @default(true)
  lowInventoryThreshold   Int      @default(10)
  frequency               String   @default("daily")
  emailNotifications      Boolean  @default(true)
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt
}

model DismissedAlerts {
  id          BigInt   @id @default(autoincrement())
  shopDomain  String
  alertId     String
  dismissedAt DateTime @default(now())

  @@unique([shopDomain, alertId])
}

model InventorySettings {
  id                    BigInt   @id @default(autoincrement())
  shopDomain            String   @unique
  autoHideOutOfStock    Boolean  @default(true)
  promoteHighInventory  Boolean  @default(true)
  lowInventoryThreshold Int      @default(10)
  highInventoryThreshold Int     @default(100)
  updateFrequency       String   @default("daily")
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
}

model ProductPerformance {
  id               BigInt   @id @default(autoincrement())
  shopDomain       String
  productId        String
  collectionId     String
  viewCount        Int      @default(0)
  clickCount       Int      @default(0)
  conversionCount  Int      @default(0)
  performanceScore Int      @default(0)
  position         Int      @default(0)
  lastUpdated      DateTime @default(now())

  @@unique([shopDomain, productId, collectionId])
}

model CollectionAnalytics {
  id                BigInt   @id @default(autoincrement())
  shopDomain        String
  collectionId      String
  date              DateTime @db.Date
  views             Int      @default(0)
  uniqueViews       Int      @default(0)
  clicks            Int      @default(0)
  conversions       Int      @default(0)
  revenue           Decimal  @default(0.00) @db.Decimal(10, 2)
  bounceRate        Decimal  @default(0.00) @db.Decimal(5, 2)
  averageTimeOnPage Int      @default(0)
  createdAt         DateTime @default(now())

  @@unique([shopDomain, collectionId, date])
}

model AlertHistory {
  id               BigInt    @id @default(autoincrement())
  shopDomain       String
  alertType        String
  alertTitle       String
  alertDescription String?
  collectionId     String?
  productId        String?
  severity         String    @default("info")
  isRead           Boolean   @default(false)
  isDismissed      Boolean   @default(false)
  actionTaken      String?
  createdAt        DateTime  @default(now())
  dismissedAt      DateTime?
}

model ProductVisibility {
  id           BigInt   @id @default(autoincrement())
  shopDomain   String
  productId    String
  collectionId String
  isVisible    Boolean  @default(true)
  hiddenReason String?
  lastUpdated  DateTime @default(now())
  updatedBy    String   @default("system")

  @@unique([shopDomain, productId, collectionId])
}
