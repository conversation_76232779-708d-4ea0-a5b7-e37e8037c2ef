import { useCallback, useState, useEffect, useRef } from "react";
import { authenticate } from "../shopify.server";
import { json } from "@remix-run/node";
import {
  useLoaderData,
  useSubmit,
  useActionData,
  useNavigate,
} from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  Select,
  Box,
  BlockStack,
  InlineStack,
  Divider,
  Grid,
  SkeletonBodyText,
  SkeletonDisplayText,
  Icon,
  Badge,
  ProgressBar,
  EmptyState,
  Button,
  Tooltip,
  Link,
  Tabs,
} from "@shopify/polaris";
import { ViewIcon, AppsFilledIcon } from "@shopify/polaris-icons";
import { Modal, TitleBar } from "@shopify/app-bridge-react";
import { useAppBridge } from "@shopify/app-bridge-react";

import { dashboardCollectionFetch } from "../models/dashboardCollectionFetch.server";
import content from "../locales/en.json";
import { fetchTaskStatus } from "../models/fetchTaskStatusById.server";
import { usePageViewTracking, trackButtonClick } from "../helper/analytics";
import { checkCollectionLimit } from "../models/collectionLimitCheck.server";
import { useModalManager } from "../hooks/useModalManager";

export const loader = async ({ request }) => {
  const { session, admin } = await authenticate.admin(request);
  const shopName = session.shop;
  const limit = await checkCollectionLimit(admin);
  return json({ shopName, collectionLimit: limit.data });
};

export const action = async ({ request }) => {
  const { session, admin } = await authenticate.admin(request);
  const data = { ...Object.fromEntries(await request.formData()) };
  let actionData;

  try {
    switch (data.type) {
      case "fetchDateBasedCollection":
        actionData = await dashboardCollectionFetch(admin, data.data);
        break;

      case "fetchCollectionAndProductData":
        actionData = await dashboardCollectionFetch(admin, "overall");
        break;

      case "task-status":
        // Handle task status checking
        actionData = await fetchTaskStatus(admin, data.id);
        break;

      default:
        return json({
          status: 400,
          message: "Invalid action type",
          data: null,
        });
    }

    return json({
      status: actionData?.status,
      message: actionData?.message,
      data: actionData?.data,
      type: data.type,
    });
  } catch (error) {
    return json({
      status: 500,
      message: "An error occurred",
      data: null,
      type: data.type,
    });
  }
};

// Custom Hook for managing dashboard data and logic
const useDashboardData = (shopName) => {
  const submit = useSubmit();
  const actionData = useActionData();
  const [collection, setCollection] = useState();
  const [pageLoading, setPageLoading] = useState(true);
  const [shopDomain, setShopDomain] = useState("");
  const [dateSelected, setDateSelected] = useState("overall");

  // Function to handle initial data fetch - can be called externally
  const handleInitialFetch = useCallback(() => {
    trackButtonClick("Refresh Dashboard Data", "Dashboard", {
      shop_name: shopName,
    });
    const formData = { type: "fetchCollectionAndProductData" };
    submit(formData, { method: "post" });
    setPageLoading(true);
  }, [submit, shopName]);

  // Initial data fetch
  useEffect(() => {
    trackButtonClick("Initial Data Fetch", "Dashboard", {
      shop_name: shopName,
    });
    const formData = { type: "fetchCollectionAndProductData" };

    submit(formData, { method: "post" });
  }, [submit, shopName]);

  // Process action data updates - Modified to ignore task-status responses
  useEffect(() => {
    if (actionData?.status === 200) {
      if (
        actionData.type === "fetchDateBasedCollection" ||
        actionData.type === "fetchCollectionAndProductData"
      ) {
        setCollection(actionData?.data?.collectionData);
        setShopDomain(actionData?.data?.storeDomain);
        setPageLoading(false);
      }
    } else if (
      (actionData?.type === "fetchCollectionAndProductData" ||
        actionData?.type === "fetchDateBasedCollection") &&
      actionData?.status !== 200
    ) {
      setPageLoading(false);
      console.error("Error fetching dashboard data:", actionData);
    }
  }, [actionData]);

  // Handler for date selection change
  const handleDateChange = useCallback(
    (value) => {
      trackButtonClick("Date Based Collection Filter", "Dashboard", {
        shop_name: shopName,
      });
      setDateSelected(value);
      const formData = { type: "fetchDateBasedCollection", data: value };
      submit(formData, { method: "post" });
      setPageLoading(true);
    },
    [submit, shopName],
  );

  // Date options derived from static content
  const dateOptions = content.Rank_collections.dashboard.filter_options.map(
    (items) => ({ label: items.label, value: items.value }),
  );

  return {
    collection,
    pageLoading,
    shopDomain,
    dateSelected,
    handleDateChange,
    dateOptions,
    handleInitialFetch, // Export the refresh function
  };
};

// Custom Hook for task status polling (non-blocking)
const useTaskStatusPolling = (shopName, onTaskComplete) => {
  const submit = useSubmit();
  const actionData = useActionData();
  const shopify = useAppBridge();
  const [taskId, setTaskId] = useState(null);
  const [isPolling, setIsPolling] = useState(false);
  const initialFetchCalled = useRef(false);
  const singleTimeCallRef = useRef(false);

  // Check for existing task on component mount
  useEffect(() => {
    const existingTaskId = localStorage.getItem("manual-generate-task-id");
    if (existingTaskId) {
      setTaskId(existingTaskId);
      setIsPolling(true);
      handleCheckTaskStatus(existingTaskId);
    }
  }, []);

  const handleCheckTaskStatus = useCallback(
    (taskIdToCheck) => {
      const currentTaskId = taskIdToCheck || taskId;
      if (currentTaskId) {
        const formData = { type: "task-status", id: currentTaskId };
        submit(formData, { method: "post" });
      }
    },
    [submit, taskId],
  );

  // Handle task status responses
  useEffect(() => {
    if (actionData?.type === "task-status" && actionData?.status === 200) {
      if (actionData?.data === "STARTED" && !initialFetchCalled.current) {
        // Task started, call the refresh callback and continue polling
        initialFetchCalled.current = true;
        if (onTaskComplete) {
          onTaskComplete();
        }
        setTimeout(() => handleCheckTaskStatus(), 10000);
      } else if (
        actionData?.data === "COMPLETED" ||
        actionData?.data === "FAILED" ||
        actionData?.data === "NO_COLLECTIONS"
      ) {
        // Task finished, cleanup
        localStorage.removeItem("manual-generate-task-id");
        setTaskId(null);
        setIsPolling(false);
        initialFetchCalled.current = false;
        singleTimeCallRef.current = false;

        // Call the refresh callback to update dashboard data
        if (onTaskComplete) {
          onTaskComplete();
        }

        // Show user-friendly notifications
        if (actionData?.data === "COMPLETED") {
          shopify.toast.show("Collection generation completed successfully!", {
            isError: false,
          });
        } else if (
          actionData?.data === "NO_COLLECTIONS" ||
          actionData?.data === "FAILED"
        ) {
          shopify.toast.show(
            "No valid products available for the given keyword or task failed",
            { isError: true },
          );
          console.error("Task failed or no collections found");
        }
      } else {
        // Continue polling for other statuses (like PENDING)
        setTimeout(() => handleCheckTaskStatus(), 10000);
      }
    } else if (
      actionData?.type === "task-status" &&
      actionData?.status !== 200
    ) {
      if (!singleTimeCallRef.current) {
        // Handle error case
        singleTimeCallRef.current = true;
        initialFetchCalled.current = true;
        setIsPolling(false);

        // Also call refresh callback on error to ensure data is up to date
        if (onTaskComplete) {
          onTaskComplete();
        }
      }
    }
  }, [actionData, handleCheckTaskStatus, onTaskComplete, shopify]);

  const startPolling = useCallback(
    (newTaskId) => {
      setTaskId(newTaskId);
      setIsPolling(true);
      localStorage.setItem("manual-generate-task-id", newTaskId);
      handleCheckTaskStatus(newTaskId);
    },
    [handleCheckTaskStatus],
  );

  return {
    taskId,
    isPolling,
    startPolling,
  };
};

// Helper components for the dashboard
const MetricCard = ({
  icon,
  title,
  value,
  loading,
  suffix = "",
  color = "default",
  description = null,
}) => {
  const iconColors = {
    default: "highlight",
    success: "success",
    warning: "warning",
    critical: "critical",
  };

  return (
    <Card sectioned>
      <BlockStack gap="200">
        <InlineStack gap="200" align="center" blockAlign="center">
          <Box>
            <Icon source={icon} color={iconColors[color]} />
          </Box>
          <Text variant="bodyMd" as="p" fontWeight="medium">
            {title}
          </Text>
        </InlineStack>
        {loading ? (
          <SkeletonDisplayText size="small" />
        ) : (
          <BlockStack gap="100">
            <Text
              alignment="center"
              variant="heading2xl"
              as="p"
              fontWeight="bold"
            >
              {value}
              {suffix}
            </Text>
            {description && (
              <Text alignment="center" variant="bodySm" as="p" color="subdued">
                {description}
              </Text>
            )}
          </BlockStack>
        )}
      </BlockStack>
    </Card>
  );
};

const CollectionsList = ({
  collections,
  loading,
  title,
  emptyStateText,
  renderItem,
  actionText,
  onAction,
}) => {
  return (
    <Card>
      <Box padding="400">
        <BlockStack gap="400">
          <InlineStack align="space-between" blockAlign="center">
            <Text variant="headingMd" as="h2" fontWeight="semibold">
              {title}
            </Text>
            {actionText && onAction && (
              <Button plain onClick={onAction}>
                <InlineStack gap="100" blockAlign="center">
                  <Text variant="bodyMd" as="span" fontWeight="medium">
                    {actionText}
                  </Text>
                  <Box>
                    <Icon source={AppsFilledIcon} color="base" />
                  </Box>
                </InlineStack>
              </Button>
            )}
          </InlineStack>
          <Divider />
          <Box
            style={{
              height: "300px",
              overflowY: "auto",
              scrollbarWidth: "thin",
            }}
          >
            {loading ? (
              <SkeletonBodyText lines={6} />
            ) : collections && collections.length > 0 ? (
              <BlockStack gap="400">
                {collections.map((collection, index) =>
                  renderItem(collection, index),
                )}
              </BlockStack>
            ) : (
              <EmptyState heading="" image="">
                <Text
                  variant="bodyMd"
                  as="p"
                  color="subdued"
                  alignment="center"
                >
                  {emptyStateText}
                </Text>
              </EmptyState>
            )}
          </Box>
        </BlockStack>
      </Box>
    </Card>
  );
};

export default function AnalyticsDashboard() {
  const { shopName, collectionLimit } = useLoaderData();
  const navigate = useNavigate();
  const [isUpgradeModalOpen, setIsUpgradeModalOpen] = useState(false);

  const {
    collection,
    pageLoading,
    shopDomain,
    dateSelected,
    handleDateChange,
    dateOptions,
    handleInitialFetch,
  } = useDashboardData(shopName);

  // Use the new non-blocking task polling hook
  const {
    taskId: manualGenTaskId,
    isPolling: isManualGenPolling,
    startPolling: startManualGenPolling,
  } = useTaskStatusPolling(shopName, handleInitialFetch);

  usePageViewTracking("Dashboard", { shop_name: shopName });

  // Derived metrics
  const totalCollections = collection?.length || 0;
  const publishedCollections =
    collection?.filter((c) => c.status === "published").length || 0;
  const scheduledCollections =
    collection?.filter((c) => c.status === "scheduled").length || 0;
  const publishedPercentage = totalCollections
    ? Math.round((publishedCollections / totalCollections) * 100)
    : 0;

  // Sort collections by views for top performers
  const topCollections = collection
    ? [...collection]
      .filter((c) => c.status === "published")
      .sort((a, b) => (b.view_count || 0) - (a.view_count || 0))
      .slice(0, 5)
    : [];

  // Recent collections
  const recentCollections = collection
    ? [...collection]
      .filter((c) => c.status === "published")
      .sort((a, b) => new Date(b.published_time) - new Date(a.published_time))
      .slice(0, 5)
    : [];

  // Calculate total views
  const totalViews = collection
    ? collection.reduce((sum, c) => sum + (c.view_count || 0), 0)
    : 0;

  // Handle view all collections
  const handleViewAllCollections = () => {
    trackButtonClick("View All Collections", "Dashboard", {
      shop_name: shopName,
    });
    navigate("/app/collection_drafts");
  };

  // Handle generate collections with task tracking
  const handleGenerateCollections = () => {
    if (collectionLimit === 0) {
      setIsUpgradeModalOpen(true);
    } else {
      navigate("/app/generate-collections");
    }
  };
  const modalManager = useModalManager();

  return (
    <Page
      sectioned
      title="Collection Analytics"
      titleMetadata={
        <Badge tone="info">
          <InlineStack gap="100" blockAlign="center">
            <Icon source={AppsFilledIcon} color="base" />
            <Text variant="bodyMd" as="span">
              Analytics
            </Text>
          </InlineStack>
        </Badge>
      }
      primaryAction={{
        content: "Generate Collections",
        onAction: handleGenerateCollections,
        disabled: isManualGenPolling || manualGenTaskId !== null,
      }}
    >
      <BlockStack gap="600">
        {/* Time Period Selector */}
        <Card>
          <Box padding="400">
            <InlineStack align="space-between" blockAlign="center">
              <InlineStack blockAlign="center" gap="300">
                <Icon source={AppsFilledIcon} color="highlight" />
                <Text variant="headingMd" as="h2">
                  Time Period
                </Text>
              </InlineStack>
              <Box minWidth="240px">
                <Select
                  label="Select time period"
                  labelHidden
                  options={dateOptions}
                  onChange={handleDateChange}
                  value={dateSelected}
                  disabled={pageLoading}
                />
              </Box>
            </InlineStack>
          </Box>
        </Card>

        {/* Task Status Indicator */}
        {isManualGenPolling && (
          <Card>
            <Box padding="400">
              <InlineStack gap="300" blockAlign="center">
                <Box>
                  <Icon source={AppsFilledIcon} color="warning" />
                </Box>
                <BlockStack gap="100">
                  <Text variant="bodyMd" as="p" fontWeight="medium">
                    Collection generation in progress...
                  </Text>
                  <Text variant="bodySm" as="p" color="subdued">
                    Your collections are being generated. This may take a few
                    minutes.
                  </Text>
                </BlockStack>
              </InlineStack>
            </Box>
          </Card>
        )}

        {/* Key Metrics Section */}
        <BlockStack gap="500">
          <Text variant="headingMd" as="h2">
            Key Metrics
          </Text>
          <Grid>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
              <MetricCard
                icon={AppsFilledIcon}
                title="Total Collections"
                value={totalCollections}
                loading={pageLoading}
                description="All collections in your store"
              />
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
              <MetricCard
                icon={AppsFilledIcon}
                title="Published Collections"
                value={publishedCollections}
                loading={pageLoading}
                color="success"
                description="Live on your storefront"
              />
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
              <MetricCard
                icon={AppsFilledIcon}
                title="Scheduled Collections"
                value={scheduledCollections}
                loading={pageLoading}
                color="warning"
                description="Waiting to be published"
              />
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
              <MetricCard
                icon={AppsFilledIcon}
                title="Total Views"
                value={totalViews}
                loading={pageLoading}
                description="Across all collections"
              />
            </Grid.Cell>
          </Grid>
        </BlockStack>

        {/* Publishing Progress */}
        <Card>
          <Box padding="400">
            <BlockStack gap="400">
              <InlineStack align="space-between" blockAlign="center">
                <InlineStack gap="200" blockAlign="center">
                  <Icon source={AppsFilledIcon} color="highlight" />
                  <Text variant="headingMd" as="h2" fontWeight="semibold">
                    Publishing Progress
                  </Text>
                </InlineStack>
                <Text variant="bodyMd" as="p">
                  {publishedCollections} of {totalCollections} collections
                  published
                </Text>
              </InlineStack>
              <BlockStack gap="200">
                <ProgressBar
                  progress={publishedPercentage}
                  size="large"
                  tone="success"
                />
                <InlineStack align="space-between">
                  <Text variant="bodySm" as="p" color="subdued">
                    {publishedPercentage}% complete
                  </Text>
                  <Button onClick={handleViewAllCollections} plain>
                    View all collections
                  </Button>
                </InlineStack>
              </BlockStack>
            </BlockStack>
          </Box>
        </Card>

        {/* Main Content Grid */}
        <Grid>
          {/* Top Performing Collections */}
          <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 6, lg: 8, xl: 8 }}>
            <CollectionsList
              title="Top Performing Collections"
              collections={topCollections}
              loading={pageLoading}
              emptyStateText="No collection performance data available yet"
              actionText="View all"
              onAction={handleViewAllCollections}
              renderItem={(collection, index) => (
                <Card key={index}>
                  <Box padding="300">
                    <InlineStack align="space-between" blockAlign="center">
                      <BlockStack gap="100">
                        <Text variant="bodyMd" as="p" fontWeight="semibold">
                          {collection.collection_name}
                        </Text>
                        <Text variant="bodySm" as="p" color="subdued">
                          {collection.collection_url}
                        </Text>
                      </BlockStack>
                      <InlineStack gap="200" blockAlign="center">
                        <Icon source={ViewIcon} color="subdued" />
                        <Text variant="bodyMd" as="p" fontWeight="semibold">
                          {collection.view_count || 0}
                        </Text>
                      </InlineStack>
                    </InlineStack>
                  </Box>
                </Card>
              )}
            />
          </Grid.Cell>

          {/* Recent Activity */}
          <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 6, lg: 4, xl: 4 }}>
            <CollectionsList
              title="Recent Activity"
              collections={recentCollections}
              loading={pageLoading}
              emptyStateText="No recent collection activity"
              renderItem={(collection, index) => (
                <Card key={index}>
                  <Box padding="300">
                    <InlineStack
                      align="space-between"
                      blockAlign="start"
                      wrap={false}
                    >
                      <InlineStack gap="200" blockAlign="start" wrap={false}>
                        <Box>
                          <Icon source={AppsFilledIcon} color="success" />
                        </Box>
                        <BlockStack gap="100">
                          <Text variant="bodyMd" as="p">
                            Collection{" "}
                            <strong>{collection.collection_name}</strong> was
                            published
                          </Text>
                          <Text variant="bodySm" as="p" color="subdued">
                            {new Date(
                              collection.published_time,
                            ).toLocaleDateString()}
                          </Text>
                        </BlockStack>
                      </InlineStack>
                    </InlineStack>
                  </Box>
                </Card>
              )}
            />
          </Grid.Cell>
        </Grid>

        <Card sectioned>
          <BlockStack gap="200">
            <InlineStack gap="200" blockAlign="center">
              <Box>
                <Icon source={AppsFilledIcon} color="highlight" />
              </Box>
              <Text variant="headingMd" as="h2" fontWeight="semibold">
                Coming Soon
              </Text>
            </InlineStack>
            <Divider />
          </BlockStack>
          <br />
          <Box style={{ display: "flex", height: "100%" }}>
            <Box
              style={{
                display: "flex",
                flex: 1,
                flexDirection: "column",
                gap: "10px",
              }}
            >
              <Text variant="headingMd" as="h3" fontWeight="semibold">
                Conversion Tracking
              </Text>
              <Text variant="bodyMd" as="p" color="subdued">
                Track how your collections impact conversion rates and sales
              </Text>
              <Box flex="1" style={{ marginTop: "auto" }}>
                <Badge tone="info">Coming Soon</Badge>
              </Box>
            </Box>
            <Box
              style={{
                display: "flex",
                flex: 1,
                flexDirection: "column",
                gap: "10px",
              }}
            >
              <Text variant="headingMd" as="h3" fontWeight="semibold">
                Customer Insights
              </Text>
              <Text variant="bodyMd" as="p" color="subdued">
                Understand customer behavior with your collections
              </Text>
              <Box flex="1" style={{ marginTop: "auto" }}>
                <Badge tone="info">Coming Soon</Badge>
              </Box>
            </Box>
            <Box
              style={{
                display: "flex",
                flex: 1,
                flexDirection: "column",
                gap: "10px",
              }}
            >
              <Text variant="headingMd" as="h3" fontWeight="semibold">
                Advanced Analytics
              </Text>
              <Text variant="bodyMd" as="p" color="subdued">
                Detailed reports and performance metrics
              </Text>
              <Box style={{ marginTop: "auto" }}>
                <Badge tone="info">Coming Soon</Badge>
              </Box>
            </Box>
          </Box>
        </Card>
      </BlockStack>
      <Box style={{ marginBottom: "2rem" }}></Box>

      <Modal
        open={isUpgradeModalOpen}
        onHide={() => setIsUpgradeModalOpen(false)}
      >
        <TitleBar title={content.Rank_collections.home.modal_heading_4}>
          <button onClick={() => navigate("/app/plans")} variant="primary">
            {content.Rank_collections.home.modal_button_1}
          </button>
        </TitleBar>
        <Box style={{ padding: "10px" }}>
          <Text variant="bodyMd" as="p">
            {content.Rank_collections.home.modal_content_3}
          </Text>
        </Box>
      </Modal>
    </Page>
  );
}
