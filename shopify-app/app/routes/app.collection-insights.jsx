import { useCallback, useState, useEffect } from "react";
import { authenticate } from "../shopify.server";
import { json } from "@remix-run/node";
import {
  useLoaderData,
  useSubmit,
  useActionData,
  useNavigate,
} from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  Select,
  Box,
  BlockStack,
  InlineStack,
  Divider,
  Grid,
  SkeletonBodyText,
  Icon,
  Badge,
  Button,
  Tabs,
} from "@shopify/polaris";
import { AnalyticsIcon, InventoryIcon, BellIcon } from "@shopify/polaris-icons";

import CollectionHeatmap from "../components/CollectionHeatmap";
import PerformanceAlerts from "../components/PerformanceAlerts";
import InventoryManager from "../components/InventoryManager";

import { fetchCollectionPerformance, updateProductOrder } from "../models/collectionPerformance.server";
import { generatePerformanceAlerts, getAlertSettings, updateAlertSettings, dismissAlert } from "../models/performanceAlerts.server";
import { getInventoryData, updateProductVisibility, bulkInventoryAction, getInventorySettings, updateInventorySettings } from "../models/inventoryManager.server";
import { usePageViewTracking, trackButtonClick } from "../helper/analytics";
import content from "../locales/en.json";

export const loader = async ({ request }) => {
  const { session, admin } = await authenticate.admin(request);
  const shopName = session.shop;
  
  // Get initial settings
  const alertSettings = await getAlertSettings(shopName);
  const inventorySettings = await getInventorySettings(shopName);
  
  return json({ 
    shopName, 
    alertSettings: alertSettings,
    inventorySettings: inventorySettings
  });
};

export const action = async ({ request }) => {
  const { session, admin } = await authenticate.admin(request);
  const data = { ...Object.fromEntries(await request.formData()) };
  let actionData;

  try {
    switch (data.type) {
      case "fetch-collection-performance":
        actionData = await fetchCollectionPerformance(admin, data.collectionId);
        break;

      case "update-product-order":
        const productOrder = JSON.parse(data.productOrder);
        actionData = await updateProductOrder(admin, data.collectionId, productOrder);
        break;

      case "generate-alerts":
        actionData = await generatePerformanceAlerts(admin);
        break;

      case "dismiss-alert":
        actionData = await dismissAlert(session.shop, data.alertId);
        break;

      case "update-alert-settings":
        const alertSettings = JSON.parse(data.settings);
        actionData = await updateAlertSettings(session.shop, alertSettings);
        break;

      case "get-inventory-data":
        actionData = await getInventoryData(admin);
        break;

      case "toggle-product-visibility":
        actionData = await updateProductVisibility(admin, data.productId, data.isVisible === 'true');
        break;

      case "bulk-inventory-action":
        const productIds = data.productIds ? JSON.parse(data.productIds) : [];
        actionData = await bulkInventoryAction(admin, data.action, productIds);
        break;

      case "update-inventory-settings":
        const inventorySettings = JSON.parse(data.settings);
        actionData = await updateInventorySettings(session.shop, inventorySettings);
        break;

      default:
        return json({
          status: 400,
          message: "Invalid action type",
          data: null,
        });
    }

    return json({
      status: actionData?.status,
      message: actionData?.message,
      data: actionData?.data,
      type: data.type,
    });
  } catch (error) {
    return json({
      status: 500,
      message: "An error occurred",
      data: null,
      type: data.type,
    });
  }
};

export default function CollectionInsights() {
  const { shopName, alertSettings, inventorySettings } = useLoaderData();
  const submit = useSubmit();
  const actionData = useActionData();
  const navigate = useNavigate();

  const [selectedTab, setSelectedTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [collectionPerformance, setCollectionPerformance] = useState(null);
  const [alerts, setAlerts] = useState([]);
  const [inventoryData, setInventoryData] = useState([]);
  const [currentAlertSettings, setCurrentAlertSettings] = useState(alertSettings);
  const [currentInventorySettings, setCurrentInventorySettings] = useState(inventorySettings);

  usePageViewTracking("Collection Insights", { shop_name: shopName });

  // Load initial data
  useEffect(() => {
    handleGenerateAlerts();
    handleGetInventoryData();
  }, []);

  // Handle action responses
  useEffect(() => {
    if (actionData?.status === 200) {
      switch (actionData.type) {
        case "fetch-collection-performance":
          setCollectionPerformance(actionData.data);
          setLoading(false);
          break;
        case "generate-alerts":
          setAlerts(actionData.data);
          break;
        case "get-inventory-data":
          setInventoryData(actionData.data);
          break;
        case "update-alert-settings":
          setCurrentAlertSettings(actionData.data);
          break;
        case "update-inventory-settings":
          setCurrentInventorySettings(actionData.data);
          break;
      }
    }
  }, [actionData]);

  const handleGenerateAlerts = useCallback(() => {
    const formData = { type: "generate-alerts" };
    submit(formData, { method: "post" });
  }, [submit]);

  const handleDismissAlert = useCallback((alertId) => {
    const formData = { type: "dismiss-alert", alertId };
    submit(formData, { method: "post" });
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
  }, [submit]);

  const handleAlertAction = useCallback((alert) => {
    trackButtonClick("Alert Action", "Collection Insights", {
      shop_name: shopName,
      alert_type: alert.type,
      action: alert.actionType
    });
    
    // Handle different alert actions
    switch (alert.actionType) {
      case 'optimize':
        navigate(`/app/collection-insights?collection=${alert.collectionId}&tab=heatmap`);
        break;
      case 'inventory':
        setSelectedTab(2); // Switch to inventory tab
        break;
      default:
        console.log('Unhandled alert action:', alert.actionType);
    }
  }, [navigate, shopName]);

  const handleUpdateAlertSettings = useCallback((newSettings) => {
    const formData = { 
      type: "update-alert-settings", 
      settings: JSON.stringify(newSettings) 
    };
    submit(formData, { method: "post" });
  }, [submit]);

  const handleGetInventoryData = useCallback(() => {
    const formData = { type: "get-inventory-data" };
    submit(formData, { method: "post" });
  }, [submit]);

  const handleToggleProductVisibility = useCallback((productId, isVisible) => {
    const formData = { 
      type: "toggle-product-visibility", 
      productId, 
      isVisible: isVisible.toString() 
    };
    submit(formData, { method: "post" });
    
    // Optimistically update the UI
    setInventoryData(prev => 
      prev.map(product => 
        product.id === productId 
          ? { ...product, isVisible } 
          : product
      )
    );
  }, [submit]);

  const handleBulkInventoryAction = useCallback((action, productIds) => {
    const formData = { 
      type: "bulk-inventory-action", 
      action,
      productIds: JSON.stringify(productIds)
    };
    submit(formData, { method: "post" });
    
    // Refresh inventory data after bulk action
    setTimeout(() => handleGetInventoryData(), 1000);
  }, [submit, handleGetInventoryData]);

  const handleUpdateInventorySettings = useCallback((newSettings) => {
    const formData = { 
      type: "update-inventory-settings", 
      settings: JSON.stringify(newSettings) 
    };
    submit(formData, { method: "post" });
  }, [submit]);

  const tabs = [
    {
      id: 'alerts',
      content: 'Performance Alerts',
      badge: alerts.length > 0 ? alerts.length.toString() : undefined,
      panelID: 'alerts-panel',
    },
    {
      id: 'heatmap',
      content: 'Collection Heatmap',
      panelID: 'heatmap-panel',
    },
    {
      id: 'inventory',
      content: 'Inventory Management',
      panelID: 'inventory-panel',
    },
  ];

  return (
    <Page
      title="Collection Insights"
      titleMetadata={
        <Badge tone="info">
          <InlineStack gap="100" blockAlign="center">
            <Icon source={AnalyticsIcon} color="base" />
            <Text variant="bodyMd" as="span">
              Advanced Analytics
            </Text>
          </InlineStack>
        </Badge>
      }
      backAction={{
        content: 'Dashboard',
        onAction: () => navigate('/app/dashboard'),
      }}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Tabs 
              tabs={tabs} 
              selected={selectedTab} 
              onSelect={setSelectedTab}
            >
              <Box padding="400">
                {selectedTab === 0 && (
                  <PerformanceAlerts
                    alerts={alerts}
                    onDismissAlert={handleDismissAlert}
                    onAlertAction={handleAlertAction}
                    settings={currentAlertSettings}
                    onUpdateSettings={handleUpdateAlertSettings}
                  />
                )}
                
                {selectedTab === 1 && (
                  <CollectionHeatmap
                    collection={collectionPerformance?.collection}
                    loading={loading}
                    onReorder={(newOrder) => {
                      if (collectionPerformance?.collection?.id) {
                        const formData = {
                          type: "update-product-order",
                          collectionId: collectionPerformance.collection.id,
                          productOrder: JSON.stringify(newOrder)
                        };
                        submit(formData, { method: "post" });
                      }
                    }}
                  />
                )}
                
                {selectedTab === 2 && (
                  <InventoryManager
                    products={inventoryData}
                    loading={loading}
                    onToggleProductVisibility={handleToggleProductVisibility}
                    onUpdateProductInventory={(productId) => {
                      // Navigate to Shopify admin for inventory updates
                      window.open(`https://${shopName}/admin/products/${productId.replace('gid://shopify/Product/', '')}`, '_blank');
                    }}
                    onBulkAction={handleBulkInventoryAction}
                    settings={currentInventorySettings}
                    onUpdateSettings={handleUpdateInventorySettings}
                  />
                )}
              </Box>
            </Tabs>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
