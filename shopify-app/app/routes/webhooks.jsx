import { authenticate } from "../shopify.server";
import db from "../db.server";
import { DeliveryMethod } from "@shopify/shopify-app-remix/server";
import pino from "pino";
import { supabase } from "../db/supabase_insert_helper";
import { json } from "@remix-run/node";
import { loggerError, loggerInfo } from "../helper/loggerHelper";
import { slackAlert } from "../models/slackAlert.server";
import { saveProductDetailsToLocalDB } from "../models/saveProductsToLocalStorage.server";
import { productsSyncToAgent } from "../models/syncProductsToAgent.server";
import { ordersSyncToAgent } from "../models/ordersDetailSyncToAgent.server";
import { deletedProductSyncToAgent } from "../models/syncDeletedProductToAgent.server";
import { productGID } from "../configs/config";
import {orderKeys} from "../configs/orderCreateKeys";
import { extractOrderKeys } from "../helper/extractOrderKeys";
import { formatProductForAgent } from "../helper/formatProductForAgent";
import { syncUpdatedProductsToAgent } from "../models/syncUpdatedProductsToAgent.server";

export const action = async ({ request }) => {
  const { topic, shop, session, admin, payload } =
    await authenticate.webhook(request);

  if (!admin) {
    // The admin context isn't returned if the webhook fired after a shop was uninstalled.
    throw new Response();
  }

  // The topics handled here should be declared in the shopify.app.toml.
  // More info: https://shopify.dev/docs/apps/build/cli-for-apps/app-configuration
  switch (topic) {
    case "APP_UNINSTALLED":
      console.log("check uninstalled............");
      console.log("The shop is", shop);

      if (session) {
        const { data: storeData, error: storeDataError } = await supabase
          .from("stores")
          .update({
            payment_status: null,
            charge_id: null,
            is_store_connected: false,
            is_product_synced: false,
            product_count: null,
            collection_count: null,
            engine_api_token: null,
            manual_update: false,
            seo_optimization: null,
            priority_support: false,
            automatic_update: false,
            onboarding_status: false,
            is_free_planned: false,
            is_growth_planned: false,
            is_advance_planned: false,
            is_automatic_plan: false,
            url_synced: false,
            collection_generated_count: 0,
          })
          .eq("store_url", shop)
          .select("*");

        if (storeDataError) {
          loggerError("Something went wrong", shop, storeDataError.message);
          return json({
            status: 400,
            message: "something went wrong",
            data: null,
          });
        }
        const { data: planData, error: planDataError } = await supabase
          .from("plans")
          .update({
            plan_name: null,
          })
          .eq("store_id", storeData[0].id);
        if (planDataError) {
          loggerError("Something went wrong", shop, planDataError.message);
          return json({
            status: 400,
            message: "something went wrong",
            data: null,
          });
        }

        const { data: preferencesData, error: preferencesDataError } =
          await supabase
            .from("preferences")
            .update({
              scheduled_status: false,
              // scheduled_frequency: null,
              // scheduled_time: null,
              // number_of_pages: null,
              // email_enabled: false,
              // auto_shuffle_enabled: false,
              // product_attributes: null,
              // seasonal_attributes: null,
              // customer_attributes: null,
              // market_attributes: null,
              // impact_type: null,
              // location_attributes: null,
              // next_generated_time: null,
              // next_scheduled_time: null,
              // next_published_time: null,
            })
            .eq("store_id", storeData[0].id);
        if (preferencesDataError) {
          loggerError(
            "Something went wrong",
            shop,
            preferencesDataError.message,
          );
          return json({
            status: 400,
            message: "Something went wrong",
            data: null,
          });
        }
        await slackAlert(shop, "uninstall");
        await db.session.deleteMany({ where: { shop } });
      }

      break;
    case "CUSTOMERS_DATA_REQUEST":
      loggerInfo(
        `Webhook triggered for the topic ${topic} for the store ${shop}`,
      );
      break;
    case "CUSTOMERS_REDACT":
      loggerInfo(
        `Webhook triggered for the topic ${topic} for the store ${shop}`,
      );
      break;
    case "SHOP_REDACT":
      loggerInfo(
        `Webhook triggered for the topic ${topic} for the store ${shop}`,
      );
      break;
    case "PRODUCTS_CREATE":
      try {
        loggerInfo("The new product is added:", shop);
        const formattedProduct = {
          data: [
            {
              id: payload.admin_graphql_api_id,
              title: payload.title,
              handle: payload.handle,
              totalInventory: payload.variants.reduce(
                (acc, variant) => acc + variant.inventory_quantity,
                0,
              ),
              tags: payload.tags !== '' ? payload.tags.split(',').map(tag => tag.trim()) : [],
              vendor: payload.vendor,
              description: payload.body_html.replace(/<\/?p>/g, ""),
              createdAt: payload.created_at,
              variants: payload.variants.map((variant) => ({
                node: {
                  id: variant.admin_graphql_api_id,
                  title: variant.title,
                  price: variant.price,
                  position: variant.position,
                  displayName: variant.title,
                },
              })),
              product_url: null,
              image_url:
                payload.images.length > 0 ? payload.images[0].src : null,
            },
          ],
        };
       const agentFormatted = formatProductForAgent(payload, shop);


        await saveProductDetailsToLocalDB(admin, "webhook", formattedProduct);
        await productsSyncToAgent(admin, agentFormatted, "webhook");
      } catch (error) {
        console.error(error, "The product create webhook error.......");
      }

    case "ORDERS_CREATE":
      loggerInfo(`Webhook triggered for the topic ${topic}`, shop);
      const filteredPayload = await extractOrderKeys(orderKeys, payload);
      await ordersSyncToAgent(shop, filteredPayload);
      break;
    
    case "PRODUCTS_DELETE":
      loggerInfo(`Webhook triggered for the topic ${topic}`, shop);
      const deletedProductWithGID = `${productGID}${payload.id}`
      await deletedProductSyncToAgent(shop, deletedProductWithGID)
      break;

    case "PRODUCTS_UPDATE":
      loggerInfo(`Webhook triggered for the topic ${topic}`, shop);
      const formatForAgent = await formatProductForAgent(payload, shop);
      await syncUpdatedProductsToAgent(shop, formatForAgent)
      break;

    default:
      throw new Response("Unhandled webhook topic", { status: 404 });
  }

  throw new Response();
};
