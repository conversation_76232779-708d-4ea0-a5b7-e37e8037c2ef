import "@shopify/shopify-app-remix/adapters/node";
import {
  ApiVersion,
  AppDistribution,
  shopifyApp,
} from "@shopify/shopify-app-remix/server";
import { DeliveryMethod } from "@shopify/shopify-app-remix/server";
import { PrismaSessionStorage } from "@shopify/shopify-app-session-storage-prisma";
import { restResources } from "@shopify/shopify-api/rest/admin/2024-07";
import prisma from "./db.server";
import { createTable } from "./db/create_tables";
import { saveStoreData } from "./models/saveStoreData.server";
import { saveProductDetailsToLocalDB } from "./models/saveProductsToLocalStorage.server";

const shopify = shopifyApp({
  apiKey: process.env.SHOPIFY_API_KEY,
  apiSecretKey: process.env.SHOPIFY_API_SECRET || "",
  apiVersion: ApiVersion.July24,
  scopes: process.env.SCOPES?.split(","),
  appUrl: process.env.SHOPIFY_APP_URL || "",
  authPathPrefix: "/auth",
  sessionStorage: new PrismaSessionStorage(prisma),
  distribution: AppDistribution.AppStore,
  restResources,
  webhooks: {
    APP_UNINSTALLED: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: "/webhooks",
      callback: async (topic, shop, body, webhookId) => {
        const payload = JSON.parse(body);
        
      },
    },
    CUSTOMERS_DATA_REQUEST: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: "/webhooks",
      callback: async (topic, shop, body, webhookId) => {
        const payload = JSON.parse(body);
        
      },
    },
    CUSTOMERS_REDACT: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: `/webhooks`,
      callback: async (topic, shop, body, webhookId) => {
        const payload = JSON.parse(body);
      },
    },
    SHOP_REDACT: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: `/webhooks`,
      callback: async (topic, shop, body) => {
        const payload = JSON.parse(body);
        
      },
    },
    PRODUCTS_CREATE: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: `/webhooks`,
      callback: async (topic, shop, body, webhookId) => {
        const payload = JSON.parse(body);
      },
    },
    PRODUCTS_DELETE: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: `/webhooks`,
      callback: async (topic, shop, body, webhookId) => {
        const payload = JSON.parse(body);
      }
    },
    PRODUCTS_UPDATE: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: `/webhooks`,
      callback: async (topic, shop, body, webhookId) => {
        const payload = JSON.parse(body);
      },
    },
    ORDERS_CREATE: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: `/webhooks`,
      callback: async (topic, shop, body, webhookId) => {
        const payload = JSON.parse(body);
      },
    },
    // APP_SUBSCRIPTIONS_UPDATE: {
    //   deliveryMethod: DeliveryMethod.Http,
    //   callbackUrl: `${process.env.SHOPIFY_APP_URL}/webhooks`,
    //   callback: async (topic, shop, body, webhookId) => {
    //     try {
    //       const payload = JSON.parse(body); // ✅ Parse payload correctly
          

    //       // You can add logic here to update subscription status in your DB
    //     } catch (error) {
    //       console.error("Error processing webhook:", error);
    //     }
    //   },
    // },
    APP_SUBSCRIPTIONS_UPDATE: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: `/webhooks`,
      callback: async (topic, shop, body, webhookId) => {
        try {
          const payload = JSON.parse(body); // ✅ Parse payload correctly
          
          
          // You can add logic here to update subscription status in your DB
        } catch (error) {
          console.error("Error processing webhook:", error);
        }
      },
    },
    
  },
  hooks: {
    afterAuth: async ({ session, admin }) => {
      await shopify.registerWebhooks({ session });
      await createTable();
      await saveStoreData(admin);
      await saveProductDetailsToLocalDB(admin, "overall", null)
    },
  },
  future: {
    unstable_newEmbeddedAuthStrategy: true,
  },
  ...(process.env.SHOP_CUSTOM_DOMAIN
    ? { customShopDomains: [process.env.SHOP_CUSTOM_DOMAIN] }
    : {}),
});

export default shopify;
export const apiVersion = ApiVersion.July24;
export const addDocumentResponseHeaders = shopify.addDocumentResponseHeaders;
export const authenticate = shopify.authenticate;
export const unauthenticated = shopify.unauthenticated;
export const login = shopify.login;
export const registerWebhooks = shopify.registerWebhooks;
export const sessionStorage = shopify.sessionStorage;
