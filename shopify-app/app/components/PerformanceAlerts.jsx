import React, { useState, useCallback } from 'react';
import {
  Card,
  BlockStack,
  InlineStack,
  Box,
  Text,
  Button,
  Badge,
  Icon,
  Popover,
  Select,
  TextField,
  Checkbox,
  Divider,
  Banner,
  List,
  EmptyState,
} from '@shopify/polaris';
import {
  AlertTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  SettingsIcon,
  OrderIcon
} from '@shopify/polaris-icons';

const AlertItem = ({ alert, onDismiss, onAction }) => {
  const getAlertIcon = (type) => {
    switch (type) {
      case 'critical': return AlertTriangleIcon;
      case 'warning': return AlertTriangleIcon;
      case 'success': return CheckCircleIcon;
      default: return OrderIcon;
    }
  };

  const getAlertTone = (type) => {
    switch (type) {
      case 'critical': return 'critical';
      case 'warning': return 'warning';
      case 'success': return 'success';
      default: return 'info';
    }
  };

  return (
    <Card>
      <Box padding="400">
        <InlineStack align="space-between" blockAlign="start">
          <InlineStack gap="300" blockAlign="start">
            <Box>
              <Icon source={getAlertIcon(alert.type)} tone={getAlertTone(alert.type)} />
            </Box>
            <BlockStack gap="200">
              <InlineStack gap="200" blockAlign="center">
                <Text variant="bodyMd" fontWeight="semibold">
                  {alert.title}
                </Text>
                <Badge tone={getAlertTone(alert.type)}>
                  {alert.type.charAt(0).toUpperCase() + alert.type.slice(1)}
                </Badge>
              </InlineStack>
              <Text variant="bodySm" color="subdued">
                {alert.description}
              </Text>
              <Text variant="bodySm" color="subdued">
                Collection: {alert.collectionName} • {alert.timestamp}
              </Text>
              {alert.metrics && (
                <InlineStack gap="400">
                  {Object.entries(alert.metrics).map(([key, value]) => (
                    <InlineStack key={key} gap="100" blockAlign="center">
                      <Text variant="bodySm" color="subdued">{key}:</Text>
                      <Text variant="bodySm" fontWeight="medium">{value}</Text>
                    </InlineStack>
                  ))}
                </InlineStack>
              )}
            </BlockStack>
          </InlineStack>

          <InlineStack gap="200">
            {alert.actionLabel && (
              <Button
                size="slim"
                onClick={() => onAction(alert)}
              >
                {alert.actionLabel}
              </Button>
            )}
            <Button
              size="slim"
              variant="plain"
              onClick={() => onDismiss(alert.id)}
            >
              Dismiss
            </Button>
          </InlineStack>
        </InlineStack>
      </Box>
    </Card>
  );
};

const AlertSettings = ({ isOpen, onClose, settings, onSave }) => {
  const [localSettings, setLocalSettings] = useState(settings);

  const handleSave = () => {
    onSave(localSettings);
    onClose();
  };

  const updateSetting = (key, value) => {
    setLocalSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  if (!isOpen) return null;

  return (
    <Card>
      <Box padding="400">
        <BlockStack gap="400">
          <InlineStack align="space-between">
            <Text variant="headingMd">Alert Settings</Text>
            <Button onClick={onClose}>Close</Button>
          </InlineStack>
          <Text variant="headingMd">Performance Thresholds</Text>

          <InlineStack gap="400">
            <Box style={{ flex: 1 }}>
              <TextField
                label="Low Performance Threshold (%)"
                type="number"
                value={localSettings.lowPerformanceThreshold?.toString()}
                onChange={(value) => updateSetting('lowPerformanceThreshold', parseInt(value))}
                helpText="Alert when collection performance drops below this percentage"
              />
            </Box>
            <Box style={{ flex: 1 }}>
              <TextField
                label="High Performance Threshold (%)"
                type="number"
                value={localSettings.highPerformanceThreshold?.toString()}
                onChange={(value) => updateSetting('highPerformanceThreshold', parseInt(value))}
                helpText="Alert when collection performance exceeds this percentage"
              />
            </Box>
          </InlineStack>

          <Divider />

          <Text variant="headingMd">Inventory Alerts</Text>

          <Checkbox
            label="Alert when products go out of stock"
            checked={localSettings.outOfStockAlerts}
            onChange={(checked) => updateSetting('outOfStockAlerts', checked)}
          />

          <TextField
            label="Low Inventory Threshold"
            type="number"
            value={localSettings.lowInventoryThreshold?.toString()}
            onChange={(value) => updateSetting('lowInventoryThreshold', parseInt(value))}
            helpText="Alert when product inventory falls below this number"
          />

          <Divider />

          <Text variant="headingMd">Notification Frequency</Text>

          <Select
            label="Alert Frequency"
            options={[
              { label: 'Immediate', value: 'immediate' },
              { label: 'Daily Summary', value: 'daily' },
              { label: 'Weekly Summary', value: 'weekly' },
            ]}
            value={localSettings.frequency}
            onChange={(value) => updateSetting('frequency', value)}
          />

          <Checkbox
            label="Email notifications"
            checked={localSettings.emailNotifications}
            onChange={(checked) => updateSetting('emailNotifications', checked)}
          />

          <InlineStack gap="200">
            <Button variant="primary" onClick={handleSave}>
              Save Settings
            </Button>
            <Button onClick={onClose}>
              Cancel
            </Button>
          </InlineStack>
        </BlockStack>
      </Box>
    </Card>
  );
};

export default function PerformanceAlerts({
  alerts = [],
  onDismissAlert,
  onAlertAction,
  settings,
  onUpdateSettings
}) {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [filter, setFilter] = useState('all');

  const filteredAlerts = alerts.filter(alert => {
    if (filter === 'all') return true;
    return alert.type === filter;
  });

  const criticalAlerts = alerts.filter(alert => alert.type === 'critical').length;
  const warningAlerts = alerts.filter(alert => alert.type === 'warning').length;

  const handleDismissAll = useCallback(() => {
    alerts.forEach(alert => onDismissAlert(alert.id));
  }, [alerts, onDismissAlert]);

  if (alerts.length === 0) {
    return (
      <Card>
        <Box padding="400">
          <EmptyState
            heading="No alerts"
            image=""
          >
            <Text variant="bodyMd" color="subdued">
              All your collections are performing well! We'll notify you if anything needs attention.
            </Text>
            <Box paddingBlockStart="300">
              <Button onClick={() => setIsSettingsOpen(true)}>
                Configure Alert Settings
              </Button>
            </Box>
          </EmptyState>
        </Box>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <Box padding="400">
          <BlockStack gap="400">
            <InlineStack align="space-between" blockAlign="center">
              <BlockStack gap="100">
                <Text variant="headingMd" fontWeight="semibold">
                  Performance Alerts
                </Text>
                <InlineStack gap="200">
                  {criticalAlerts > 0 && (
                    <Badge tone="critical">{criticalAlerts} Critical</Badge>
                  )}
                  {warningAlerts > 0 && (
                    <Badge tone="warning">{warningAlerts} Warning</Badge>
                  )}
                  <Text variant="bodySm" color="subdued">
                    {alerts.length} total alerts
                  </Text>
                </InlineStack>
              </BlockStack>

              <InlineStack gap="200">
                <Button
                  icon={SettingsIcon}
                  onClick={() => setIsSettingsOpen(true)}
                >
                  Settings
                </Button>
                {alerts.length > 0 && (
                  <Button variant="plain" onClick={handleDismissAll}>
                    Dismiss All
                  </Button>
                )}
              </InlineStack>
            </InlineStack>

            {alerts.length > 3 && (
              <Select
                label="Filter alerts"
                labelHidden
                options={[
                  { label: 'All Alerts', value: 'all' },
                  { label: 'Critical Only', value: 'critical' },
                  { label: 'Warnings Only', value: 'warning' },
                  { label: 'Success Only', value: 'success' },
                ]}
                value={filter}
                onChange={setFilter}
              />
            )}

            <BlockStack gap="300">
              {filteredAlerts.map((alert) => (
                <AlertItem
                  key={alert.id}
                  alert={alert}
                  onDismiss={onDismissAlert}
                  onAction={onAlertAction}
                />
              ))}
            </BlockStack>
          </BlockStack>
        </Box>
      </Card>

      <AlertSettings
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
        settings={settings}
        onSave={onUpdateSettings}
      />
    </>
  );
}
