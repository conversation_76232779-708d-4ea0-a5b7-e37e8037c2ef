import React, { useState, useCallback } from 'react';
import {
  Card,
  BlockStack,
  InlineStack,
  Box,
  Text,
  Button,
  Badge,
  Tooltip,
  Icon,
  Modal,
  Grid,
  SkeletonBodyText,
  EmptyState,
} from '@shopify/polaris';
import { DragDropIcon, ViewIcon, CartIcon } from '@shopify/polaris-icons';

const ProductHeatmapItem = ({ product, index, isDragging }) => {
  const getPerformanceColor = (score) => {
    if (score >= 80) return '#00A047'; // Green
    if (score >= 60) return '#FFC453'; // Yellow
    if (score >= 40) return '#FF8A00'; // Orange
    return '#D72C0D'; // Red
  };

  const getPerformanceBadge = (score) => {
    if (score >= 80) return { tone: 'success', text: 'High' };
    if (score >= 60) return { tone: 'warning', text: 'Medium' };
    if (score >= 40) return { tone: 'attention', text: 'Low' };
    return { tone: 'critical', text: 'Poor' };
  };

  const performanceScore = product.performance_score || 0;
  const badge = getPerformanceBadge(performanceScore);

  return (
    <Card
      sectioned
      background={isDragging ? 'bg-surface-selected' : 'bg-surface'}
      borderColor={isDragging ? 'border-brand' : undefined}
    >
      <InlineStack align="space-between" blockAlign="center">
        <InlineStack gap="300" blockAlign="center">
          <Box>
            <Icon source={DragDropIcon} color="subdued" />
          </Box>
          <Box
            style={{
              width: '60px',
              height: '60px',
              backgroundColor: '#f6f6f7',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {product.image_url ? (
              <img
                src={product.image_url}
                alt={product.title}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  borderRadius: '8px',
                }}
              />
            ) : (
              <Text variant="bodySm" color="subdued">
                No Image
              </Text>
            )}
          </Box>
          <BlockStack gap="100">
            <Text variant="bodyMd" fontWeight="semibold">
              {product.title}
            </Text>
            <Text variant="bodySm" color="subdued">
              Position: {index + 1}
            </Text>
          </BlockStack>
        </InlineStack>
        
        <InlineStack gap="200" blockAlign="center">
          <Tooltip content={`Performance Score: ${performanceScore}%`}>
            <Badge tone={badge.tone}>{badge.text}</Badge>
          </Tooltip>
          
          <InlineStack gap="100" blockAlign="center">
            <Icon source={ViewIcon} color="subdued" />
            <Text variant="bodySm">{product.view_count || 0}</Text>
          </InlineStack>
          
          <InlineStack gap="100" blockAlign="center">
            <Icon source={CartIcon} color="subdued" />
            <Text variant="bodySm">{product.click_count || 0}</Text>
          </InlineStack>
          
          <Box
            style={{
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              backgroundColor: getPerformanceColor(performanceScore),
            }}
          />
        </InlineStack>
      </InlineStack>
    </Card>
  );
};

export default function CollectionHeatmap({ 
  collection, 
  loading, 
  onReorder, 
  onOptimize 
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [products, setProducts] = useState(collection?.products || []);

  const handleOptimizeOrder = useCallback(() => {
    // Sort products by performance score (highest first)
    const optimizedProducts = [...products].sort((a, b) => 
      (b.performance_score || 0) - (a.performance_score || 0)
    );
    setProducts(optimizedProducts);
    if (onReorder) {
      onReorder(optimizedProducts);
    }
  }, [products, onReorder]);

  if (loading) {
    return (
      <Card>
        <Box padding="400">
          <SkeletonBodyText lines={6} />
        </Box>
      </Card>
    );
  }

  if (!collection || !products.length) {
    return (
      <Card>
        <Box padding="400">
          <EmptyState
            heading="No products found"
            image=""
          >
            <Text variant="bodyMd" color="subdued">
              This collection doesn't have any products to analyze.
            </Text>
          </EmptyState>
        </Box>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <Box padding="400">
          <BlockStack gap="400">
            <InlineStack align="space-between" blockAlign="center">
              <BlockStack gap="100">
                <Text variant="headingMd" fontWeight="semibold">
                  Collection Performance Heatmap
                </Text>
                <Text variant="bodySm" color="subdued">
                  Colors indicate performance levels. Green = High, Yellow = Medium, Orange = Low, Red = Poor
                </Text>
              </BlockStack>
              
              <InlineStack gap="200">
                <Button onClick={handleOptimizeOrder}>
                  Auto-Optimize Order
                </Button>
                <Button 
                  variant="primary" 
                  onClick={() => setIsModalOpen(true)}
                >
                  View Details
                </Button>
              </InlineStack>
            </InlineStack>

            <BlockStack gap="200">
              {products.slice(0, 5).map((product, index) => (
                <ProductHeatmapItem
                  key={product.id}
                  product={product}
                  index={index}
                  isDragging={false}
                />
              ))}
            </BlockStack>

            {products.length > 5 && (
              <Box paddingBlockStart="200">
                <Text variant="bodySm" color="subdued" alignment="center">
                  Showing top 5 products. View all {products.length} products in detailed view.
                </Text>
              </Box>
            )}
          </BlockStack>
        </Box>
      </Card>

      <Modal
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Collection Performance Details"
        large
      >
        <Modal.Section>
          <BlockStack gap="400">
            <Grid>
              <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
                <Card>
                  <Box padding="300">
                    <BlockStack gap="100">
                      <Text variant="bodySm" color="subdued">Total Products</Text>
                      <Text variant="headingMd">{products.length}</Text>
                    </BlockStack>
                  </Box>
                </Card>
              </Grid.Cell>
              <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
                <Card>
                  <Box padding="300">
                    <BlockStack gap="100">
                      <Text variant="bodySm" color="subdued">Avg Performance</Text>
                      <Text variant="headingMd">
                        {Math.round(
                          products.reduce((sum, p) => sum + (p.performance_score || 0), 0) / products.length
                        )}%
                      </Text>
                    </BlockStack>
                  </Box>
                </Card>
              </Grid.Cell>
              <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
                <Card>
                  <Box padding="300">
                    <BlockStack gap="100">
                      <Text variant="bodySm" color="subdued">Total Views</Text>
                      <Text variant="headingMd">
                        {products.reduce((sum, p) => sum + (p.view_count || 0), 0)}
                      </Text>
                    </BlockStack>
                  </Box>
                </Card>
              </Grid.Cell>
              <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
                <Card>
                  <Box padding="300">
                    <BlockStack gap="100">
                      <Text variant="bodySm" color="subdued">Total Clicks</Text>
                      <Text variant="headingMd">
                        {products.reduce((sum, p) => sum + (p.click_count || 0), 0)}
                      </Text>
                    </BlockStack>
                  </Box>
                </Card>
              </Grid.Cell>
            </Grid>

            <Box style={{ maxHeight: '400px', overflowY: 'auto' }}>
              <BlockStack gap="200">
                {products.map((product, index) => (
                  <ProductHeatmapItem
                    key={product.id}
                    product={product}
                    index={index}
                    isDragging={false}
                  />
                ))}
              </BlockStack>
            </Box>
          </BlockStack>
        </Modal.Section>
      </Modal>
    </>
  );
}
