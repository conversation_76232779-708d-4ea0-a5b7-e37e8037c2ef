export const storesTable = `
    CREATE TABLE IF NOT EXISTS public.Stores (
    id BIGSERIAL PRIMARY KEY,
    uuid VARCHAR(150) UNIQUE,
    store_name VARCHAR(100) NOT NULL,
    store_url VARCHAR(500),
    store_email VARCHAR(500),
    is_first_time BOOLEAN NOT NULL DEFAULT TRUE,
    charge_id VARCHAR(100),
    payment_status VARCHAR(50),
    plan_created_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    plan_expires_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    onboarding_status BOOLEAN NOT NULL DEFAULT FALSE,
    reinstalled_user BOOLEAN NOT NULL DEFAULT FALSE,
    access_token TEXT,
    encryption_key TEXT,
    iv TEXT,
    onboarded_step TEXT DEFAULT 'step-1',
    google_is_logged_in BOOLEAN NOT NULL DEFAULT FALSE,
    google_analytics_key TEXT,
    google_encryption_key TEXT,
    google_account_id BIGINT,
    google_property_id BIGINT,
    google_encrypted_iv TEXT,
    is_store_connected BOOLEAN DEFAULT FALSE,
    is_product_synced BOOLEAN DEFAULT FALSE,
    product_count BIGINT,
    collection_count BIGINT,
    manual_update BOOLEAN DEFAULT FALSE,
    automatic_update BOOLEAN DEFAULT FALSE,
    seo_optimization TEXT,
    priority_support BOOLEAN DEFAULT FALSE,
    url_synced BOOLEAN,
    engine_api_token TEXT,
    is_free_planned BOOLEAN, 
    is_growth_planned BOOLEAN,
    is_advance_planned BOOLEAN,
    is_automatic_plan BOOLEAN,
    collection_generated_count BIGINT,
    store_type TEXT,
    store_located_region TEXT,
    store_located_country TEXT,
    gsc_user_data TEXT null,
    is_gsc_logged_in BOOLEAN DEFAULT FALSE,
    is_gsc_feature_enabled BOOLEAN DEFAULT FALSE,
    is_mail_report_enabled BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
`;

export const preferenceTable = `
   CREATE TABLE IF NOT EXISTS public.Preferences (
    s_no BIGSERIAL PRIMARY KEY,
    store_id BIGINT,
    uuid VARCHAR(150) UNIQUE,
    scheduled_status BOOLEAN DEFAULT TRUE,
    scheduled_frequency VARCHAR(50) DEFAULT 'everyThreeDays',
    scheduled_time VARCHAR(50),
    next_generated_time TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    next_scheduled_time TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    next_published_time TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    number_of_pages BIGINT,
    product_count BIGINT,
    email_enabled BOOLEAN DEFAULT FALSE,
    auto_shuffle_enabled BOOLEAN DEFAULT FALSE,
    product_attributes JSON,
    seasonal_attributes JSON,
    customer_attributes JSON,
    market_attributes JSON,
    location_attributes JSON,
    impact_type JSON,
    weekly_report_enabled BOOLEAN DEFAULT FALSE,
    is_generated_mail_unsubscribed BOOLEAN DEFAULT FALSE,
    is_scheduled_mail_unsubscribed BOOLEAN DEFAULT FALSE,
    is_published_mail_unsubscribed BOOLEAN DEFAULT FALSE,
    is_weekly_report_unsubscribed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_store
        FOREIGN KEY(store_id)
        REFERENCES public.Stores(id)
        ON DELETE SET NULL
);
`;


export const collections = `
    CREATE TABLE IF NOT EXISTS public.Collections (
        id BIGSERIAL PRIMARY KEY,
        store_id BIGINT,
        uuid VARCHAR(150) UNIQUE,
        collection_url VARCHAR(500),
        collection_name VARCHAR(100),
        product_details JSON,
        keywords JSON,
        description VARCHAR,
        status VARCHAR(50),
        email_enabled BOOLEAN NOT NULL DEFAULT FALSE,
        schedule_on_status BOOLEAN NOT NULL DEFAULT TRUE,
        scheduled_time TIMESTAMP WITH TIME ZONE,
        generated_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        published_time TIMESTAMP WITH TIME ZONE,
        published_type TEXT DEFAULT NULL,
        collection_id BIGINT DEFAULT NULL,
        view_count BIGINT,
        sorting_value TEXT,
        generated_type TEXT,
        task_id UUID DEFAULT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_store
            FOREIGN KEY(store_id)
            REFERENCES public.Stores(id)
            ON DELETE SET NULL
    );

    
`;

export const plan = `
    CREATE TABLE IF NOT EXISTS Public.Plans (
        id BIGSERIAL PRIMARY KEY,
        store_id BIGINT,
        plan_name TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_store
            FOREIGN KEY(store_id)
            REFERENCES public.Stores(id)
            ON DELETE SET NULL
    )
`

export const productsTable = `
    CREATE TABLE IF NOT EXISTS public.ProductsTable (
        id BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
        uuid UUID DEFAULT gen_random_uuid(),
        title TEXT,
        description TEXT,
        price REAL,
        handle TEXT,
        tags TEXT[],
        vendor TEXT,
        store_id BIGINT,
        shopify_gid TEXT,
        variants JSON[],
        image_url TEXT,
        product_url TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_store
            FOREIGN KEY (store_id)
            REFERENCES public.Stores(id)
            ON DELETE SET NULL
    )`;


export const gscData = `
CREATE TABLE IF NOT EXISTS public.gscData (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    store_id BIGINT,
    collection_id BIGINT,
    query TEXT,
    page_url TEXT,
    country TEXT,
    device TEXT,
    clicks BIGINT,
    impressions BIGINT,
    ctr BIGINT,
    position BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_store
        FOREIGN KEY (store_id)
        REFERENCES public.Stores(id)
        ON DELETE SET NULL,
    CONSTRAINT fk_collection
        FOREIGN KEY (collection_id)
        REFERENCES public.Collections(id)
        ON DELETE SET NULL
)`;




export const collectionJobsTable = `
    CREATE TABLE IF NOT EXISTS public.Collection_jobs (
        id BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
        store_id BIGINT,
        task_id UUID,
        attributes JSON default '{}',
        origin TEXT,
        task_status TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_store
            FOREIGN KEY (store_id)
            REFERENCES public.Stores(id)
            ON DELETE SET NULL
    )
`