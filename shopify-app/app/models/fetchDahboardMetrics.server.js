import axios from 'axios'
import dotenv from 'dotenv'
import { loggerInfo, loggerError } from '../helper/loggerHelper'
import { supabase } from '../db/supabase_insert_helper'
import { engineBaseUrl, engineAPIVersion, collectionRoute,fetchSubroute, dashboardMetrics  } from '../configs/config'
import { shopDetails } from '../helper/shopDetails'

dotenv.config();

const appToken = process.env.BINCHA_APP_TOKEN
const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${fetchSubroute}${dashboardMetrics}`

export const fetchDashboardMetrics = async (admin) => {
    const shop = await shopDetails(admin)
    const storeName = shop?.data?.shop?.name;
    loggerInfo('Fetch Dashboard metrics from Agent DB function is initialized', storeName)
    try{
        const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('store_name', storeName);
        if(storeDataError){
            loggerError('Something went wrong or store data not found (Fetch dashboard metrics)', storeName, storeDataError.message)
            return {
                status: 400,
                message: "Store data not found",
                data: null
            }
        }
        const apiToken = storeData[0].engine_api_token;
        const response = await axios.get(endPoint, {
            headers: {
                "X-PROACTIVE-TOKEN": apiToken,
                "X-BINCHA-APP-TOKEN": appToken
            }
        })
        loggerInfo('Dashboard metrics data fetched successfully from Agent DB', storeName)
        return {
            status: response.data.status_code,
            message: response.data.message,
            data: {
                conversion_rate: response.data.data.conversion_rate,
                opportunity_score: response.data.data.opportunity_score
            }
        }
    }catch(error){
        loggerError('Something went wrong to fetch the dashboard metrics from Agent DB', storeName, error.message)
        return {
            status: 400,
            message: "Something went wrong to fetch the dashboard metrics from Agent DB",
            data: null
        }
    }
}