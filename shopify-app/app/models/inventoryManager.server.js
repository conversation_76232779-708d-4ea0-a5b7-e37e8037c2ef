import { shopDetails } from '../helper/shopDetails';
import { loggerInfo, loggerError } from '../helper/loggerHelper';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export const getInventoryData = async (admin) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  
  loggerInfo("Fetching inventory data for collections", storeName);

  try {
    // Get all products with their inventory and collection associations
    const productsQuery = `
      query {
        products(first: 100) {
          nodes {
            id
            title
            handle
            featuredImage {
              url
            }
            totalInventory
            variants(first: 1) {
              nodes {
                inventoryQuantity
                price
              }
            }
            collections(first: 10) {
              nodes {
                id
                title
              }
            }
          }
        }
      }
    `;

    const response = await admin.graphql(productsQuery);
    const data = await response.json();

    if (!data?.data?.products?.nodes) {
      throw new Error("No products found");
    }

    const products = data.data.products.nodes.map(product => ({
      id: product.id,
      title: product.title,
      handle: product.handle,
      image: product.featuredImage?.url,
      inventory: product.totalInventory || 0,
      price: product.variants.nodes[0]?.price || '0.00',
      collections: product.collections.nodes.map(c => c.title),
      collectionIds: product.collections.nodes.map(c => c.id),
      isVisible: product.totalInventory > 0 // Default visibility based on inventory
    }));

    loggerInfo(
      "Inventory data fetched successfully",
      storeName,
      { productCount: products.length }
    );

    return {
      status: 200,
      message: "Inventory data fetched successfully",
      data: products
    };

  } catch (error) {
    loggerError(
      "Error fetching inventory data",
      storeName,
      error.message
    );

    return {
      status: 500,
      message: "Failed to fetch inventory data",
      data: []
    };
  }
};

export const updateProductVisibility = async (admin, productId, isVisible) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  
  loggerInfo(
    "Updating product visibility",
    storeName,
    { productId, isVisible }
  );

  try {
    // In Shopify, we can't directly hide products from collections,
    // but we can update the product's status or use metafields
    // For this implementation, we'll use a metafield to track visibility
    
    const metafieldMutation = `
      mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
        metafieldsSet(metafields: $metafields) {
          metafields {
            id
            key
            value
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const variables = {
      metafields: [
        {
          ownerId: productId,
          namespace: "rank_collections",
          key: "collection_visibility",
          value: isVisible.toString(),
          type: "boolean"
        }
      ]
    };

    const response = await admin.graphql(metafieldMutation, { variables });
    const result = await response.json();

    if (result.data?.metafieldsSet?.userErrors?.length > 0) {
      throw new Error(result.data.metafieldsSet.userErrors[0].message);
    }

    loggerInfo(
      "Product visibility updated successfully",
      storeName,
      { productId, isVisible }
    );

    return {
      status: 200,
      message: "Product visibility updated successfully",
      data: { productId, isVisible }
    };

  } catch (error) {
    loggerError(
      "Error updating product visibility",
      storeName,
      error.message,
      { productId }
    );

    return {
      status: 500,
      message: "Failed to update product visibility",
      data: null
    };
  }
};

export const bulkInventoryAction = async (admin, action, productIds = []) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  
  loggerInfo(
    "Performing bulk inventory action",
    storeName,
    { action, productCount: productIds.length }
  );

  try {
    let results = [];

    switch (action) {
      case 'hide':
        for (const productId of productIds) {
          const result = await updateProductVisibility(admin, productId, false);
          results.push(result);
        }
        break;

      case 'show':
        for (const productId of productIds) {
          const result = await updateProductVisibility(admin, productId, true);
          results.push(result);
        }
        break;

      case 'auto-optimize':
        results = await autoOptimizeInventory(admin);
        break;

      default:
        throw new Error(`Unknown action: ${action}`);
    }

    const successCount = results.filter(r => r.status === 200).length;

    loggerInfo(
      "Bulk inventory action completed",
      storeName,
      { action, successCount, totalCount: results.length }
    );

    return {
      status: 200,
      message: `Bulk action completed: ${successCount}/${results.length} successful`,
      data: { successCount, totalCount: results.length, results }
    };

  } catch (error) {
    loggerError(
      "Error performing bulk inventory action",
      storeName,
      error.message,
      { action }
    );

    return {
      status: 500,
      message: "Failed to perform bulk action",
      data: null
    };
  }
};

async function autoOptimizeInventory(admin) {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  
  loggerInfo("Starting auto-optimization of inventory", storeName);

  try {
    const results = [];
    
    // Get inventory settings
    const settings = await getInventorySettings(shop?.data?.shop?.myshopifyDomain);
    
    // Get current inventory data
    const inventoryData = await getInventoryData(admin);
    
    if (inventoryData.status !== 200) {
      throw new Error("Failed to fetch inventory data");
    }

    const products = inventoryData.data;

    // Auto-hide out-of-stock products if enabled
    if (settings.autoHideOutOfStock) {
      const outOfStockProducts = products.filter(p => p.inventory === 0);
      
      for (const product of outOfStockProducts) {
        const result = await updateProductVisibility(admin, product.id, false);
        results.push({
          ...result,
          action: 'hide_out_of_stock',
          productTitle: product.title
        });
      }
    }

    // Promote high-inventory products if enabled
    if (settings.promoteHighInventory) {
      const highInventoryProducts = products.filter(
        p => p.inventory >= settings.highInventoryThreshold
      );
      
      for (const product of highInventoryProducts) {
        // In a real implementation, you would reorder products in collections
        // For now, we'll just ensure they're visible
        const result = await updateProductVisibility(admin, product.id, true);
        results.push({
          ...result,
          action: 'promote_high_inventory',
          productTitle: product.title
        });
      }
    }

    loggerInfo(
      "Auto-optimization completed",
      storeName,
      { actionsPerformed: results.length }
    );

    return results;

  } catch (error) {
    loggerError(
      "Error during auto-optimization",
      storeName,
      error.message
    );
    
    return [{
      status: 500,
      message: "Auto-optimization failed",
      data: null
    }];
  }
}

export const getInventorySettings = async (shopDomain) => {
  try {
    const settings = await prisma.inventorySettings.findUnique({
      where: { shopDomain }
    });

    if (settings) {
      return settings;
    }

    // Return default settings
    return {
      shopDomain,
      autoHideOutOfStock: true,
      promoteHighInventory: true,
      lowInventoryThreshold: 10,
      highInventoryThreshold: 100,
      updateFrequency: 'daily'
    };
  } catch (error) {
    loggerError("Error getting inventory settings", shopDomain, error.message);
    
    return {
      shopDomain,
      autoHideOutOfStock: true,
      promoteHighInventory: true,
      lowInventoryThreshold: 10,
      highInventoryThreshold: 100,
      updateFrequency: 'daily'
    };
  }
};

export const updateInventorySettings = async (shopDomain, newSettings) => {
  try {
    const settings = await prisma.inventorySettings.upsert({
      where: { shopDomain },
      update: newSettings,
      create: {
        shopDomain,
        ...newSettings
      }
    });

    loggerInfo("Inventory settings updated successfully", shopDomain);

    return {
      status: 200,
      message: "Settings updated successfully",
      data: settings
    };
  } catch (error) {
    loggerError("Error updating inventory settings", shopDomain, error.message);

    return {
      status: 500,
      message: "Failed to update settings",
      data: null
    };
  }
};

export const getInventoryInsights = async (admin) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  
  try {
    const inventoryData = await getInventoryData(admin);
    
    if (inventoryData.status !== 200) {
      throw new Error("Failed to fetch inventory data");
    }

    const products = inventoryData.data;
    
    const insights = {
      totalProducts: products.length,
      inStock: products.filter(p => p.inventory > 10).length,
      lowStock: products.filter(p => p.inventory > 0 && p.inventory <= 10).length,
      outOfStock: products.filter(p => p.inventory === 0).length,
      averageInventory: Math.round(
        products.reduce((sum, p) => sum + p.inventory, 0) / products.length
      ),
      totalInventoryValue: products.reduce(
        (sum, p) => sum + (p.inventory * parseFloat(p.price)), 0
      ).toFixed(2),
      recommendations: generateInventoryRecommendations(products)
    };

    return {
      status: 200,
      message: "Inventory insights generated successfully",
      data: insights
    };

  } catch (error) {
    loggerError("Error generating inventory insights", storeName, error.message);

    return {
      status: 500,
      message: "Failed to generate insights",
      data: null
    };
  }
};

function generateInventoryRecommendations(products) {
  const recommendations = [];
  
  const outOfStockCount = products.filter(p => p.inventory === 0).length;
  const lowStockCount = products.filter(p => p.inventory > 0 && p.inventory <= 10).length;
  
  if (outOfStockCount > 0) {
    recommendations.push({
      type: 'critical',
      title: 'Hide Out-of-Stock Products',
      description: `${outOfStockCount} products are out of stock and should be hidden from collections`,
      action: 'hide_out_of_stock'
    });
  }
  
  if (lowStockCount > 0) {
    recommendations.push({
      type: 'warning',
      title: 'Restock Low Inventory Items',
      description: `${lowStockCount} products have low inventory and may need restocking`,
      action: 'restock_items'
    });
  }
  
  const highInventoryProducts = products.filter(p => p.inventory > 100);
  if (highInventoryProducts.length > 0) {
    recommendations.push({
      type: 'opportunity',
      title: 'Promote High-Inventory Products',
      description: `${highInventoryProducts.length} products have high inventory and could be promoted`,
      action: 'promote_high_inventory'
    });
  }
  
  return recommendations;
}
